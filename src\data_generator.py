"""
Spiral Data Generator for Neural Network Regression

This module provides utilities for generating synthetic spiral datasets
with continuous target values for regression tasks.
"""

import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from typing import Tuple, Optional
import pandas as pd


class SpiralDataGenerator:
    """
    Generates synthetic spiral datasets with configurable parameters.

    The spiral is generated using parametric equations with added noise.
    Target values are computed based on the distance from the spiral center
    and angular position, creating a continuous regression problem.
    """

    def __init__(self, random_state: Optional[int] = None):
        """
        Initialize the spiral data generator.

        Args:
            random_state: Random seed for reproducible results
        """
        self.random_state = random_state
        if random_state is not None:
            np.random.seed(random_state)

    def generate_spiral(self,
                       n_samples: int = 1000,
                       n_turns: float = 2.5,
                       noise_level: float = 0.1,
                       radius_scale: float = 1.0) -> Tuple[np.ndarray, np.ndarray]:
        """
        Generate a spiral dataset with continuous target values.

        Args:
            n_samples: Number of data points to generate
            n_turns: Number of complete spiral turns
            noise_level: Standard deviation of Gaussian noise added to coordinates
            radius_scale: Scaling factor for spiral radius

        Returns:
            Tuple of (X, y) where:
            - X: (n_samples, 2) array of 2D coordinates
            - y: (n_samples,) array of continuous target values
        """
        # Reset random state for reproducibility
        if self.random_state is not None:
            np.random.seed(self.random_state)

        # Generate parameter t from 0 to n_turns * 2π
        t = np.linspace(0, n_turns * 2 * np.pi, n_samples)

        # Parametric spiral equations
        # Radius increases linearly with parameter t
        radius = radius_scale * t / (n_turns * 2 * np.pi)

        # Convert to Cartesian coordinates
        x = radius * np.cos(t)
        y = radius * np.sin(t)

        # Add Gaussian noise
        x += np.random.normal(0, noise_level, n_samples)
        y += np.random.normal(0, noise_level, n_samples)

        # Create feature matrix
        X = np.column_stack([x, y])

        # Generate continuous target values
        # Target is a function of distance from center and angular position
        distance_from_center = np.sqrt(x**2 + y**2)
        angle = np.arctan2(y, x)

        # Combine distance and angle information for target
        # This creates a smooth, continuous function for regression
        target = (distance_from_center * np.sin(angle * 2) +
                 0.5 * distance_from_center * np.cos(t * 0.5) +
                 0.1 * np.sin(t * 3))

        return X, target

    def train_test_split(self, X: np.ndarray, y: np.ndarray,
                        test_size: float = 0.2,
                        val_size: float = 0.1) -> Tuple[np.ndarray, ...]:
        """
        Split data into train, validation, and test sets.

        Args:
            X: Feature matrix
            y: Target values
            test_size: Fraction of data for test set
            val_size: Fraction of data for validation set

        Returns:
            Tuple of (X_train, X_val, X_test, y_train, y_val, y_test)
        """
        n_samples = len(X)

        # Reset random state for reproducibility
        if self.random_state is not None:
            np.random.seed(self.random_state + 1)  # Use different seed for splitting

        # Create random indices
        indices = np.random.permutation(n_samples)

        # Calculate split points
        test_split = int(n_samples * (1 - test_size))
        val_split = int(test_split * (1 - val_size))

        # Split indices
        train_idx = indices[:val_split]
        val_idx = indices[val_split:test_split]
        test_idx = indices[test_split:]

        # Split data
        X_train, y_train = X[train_idx], y[train_idx]
        X_val, y_val = X[val_idx], y[val_idx]
        X_test, y_test = X[test_idx], y[test_idx]

        return X_train, X_val, X_test, y_train, y_val, y_test


class DataVisualizer:
    """
    Utilities for visualizing spiral data and model results.
    """

    def __init__(self, figsize: Tuple[int, int] = (12, 8)):
        """
        Initialize the data visualizer.

        Args:
            figsize: Default figure size for plots
        """
        self.figsize = figsize
        plt.style.use('default')  # Changed from seaborn-v0_8 for compatibility
        sns.set_palette("husl")

    def plot_spiral_data(self, X: np.ndarray, y: np.ndarray,
                        title: str = "Spiral Dataset",
                        save_path: Optional[str] = None) -> None:
        """
        Create a scatter plot of the spiral data colored by target values.

        Args:
            X: Feature matrix (n_samples, 2)
            y: Target values (n_samples,)
            title: Plot title
            save_path: Optional path to save the figure
        """
        fig, ax = plt.subplots(figsize=self.figsize)

        scatter = ax.scatter(X[:, 0], X[:, 1], c=y, cmap='viridis',
                           alpha=0.7, s=30)

        ax.set_xlabel('X1')
        ax.set_ylabel('X2')
        ax.set_title(title)
        ax.grid(True, alpha=0.3)

        # Add colorbar
        cbar = plt.colorbar(scatter, ax=ax)
        cbar.set_label('Target Value')

        plt.tight_layout()

        if save_path:
            plt.savefig(save_path, dpi=300, bbox_inches='tight')

        plt.show()

    def plot_data_splits(self, X_train: np.ndarray, X_val: np.ndarray,
                        X_test: np.ndarray, y_train: np.ndarray,
                        y_val: np.ndarray, y_test: np.ndarray,
                        save_path: Optional[str] = None) -> None:
        """
        Visualize train/validation/test splits.

        Args:
            X_train, X_val, X_test: Feature matrices for each split
            y_train, y_val, y_test: Target values for each split
            save_path: Optional path to save the figure
        """
        fig, axes = plt.subplots(1, 3, figsize=(18, 5))

        datasets = [
            (X_train, y_train, 'Training Set'),
            (X_val, y_val, 'Validation Set'),
            (X_test, y_test, 'Test Set')
        ]

        for i, (X, y, title) in enumerate(datasets):
            scatter = axes[i].scatter(X[:, 0], X[:, 1], c=y,
                                    cmap='viridis', alpha=0.7, s=30)
            axes[i].set_xlabel('X1')
            axes[i].set_ylabel('X2')
            axes[i].set_title(f'{title} (n={len(X)})')
            axes[i].grid(True, alpha=0.3)

            # Add colorbar
            cbar = plt.colorbar(scatter, ax=axes[i])
            cbar.set_label('Target Value')

        plt.tight_layout()

        if save_path:
            plt.savefig(save_path, dpi=300, bbox_inches='tight')

        plt.show()

    def plot_target_distribution(self, y: np.ndarray,
                               title: str = "Target Distribution",
                               save_path: Optional[str] = None) -> None:
        """
        Plot the distribution of target values.

        Args:
            y: Target values
            title: Plot title
            save_path: Optional path to save the figure
        """
        fig, (ax1, ax2) = plt.subplots(1, 2, figsize=self.figsize)

        # Histogram
        ax1.hist(y, bins=50, alpha=0.7, color='skyblue', edgecolor='black')
        ax1.set_xlabel('Target Value')
        ax1.set_ylabel('Frequency')
        ax1.set_title('Target Value Histogram')
        ax1.grid(True, alpha=0.3)

        # Box plot
        ax2.boxplot(y, vert=True)
        ax2.set_ylabel('Target Value')
        ax2.set_title('Target Value Box Plot')
        ax2.grid(True, alpha=0.3)

        fig.suptitle(title)
        plt.tight_layout()

        if save_path:
            plt.savefig(save_path, dpi=300, bbox_inches='tight')

        plt.show()


def create_sample_dataset(n_samples: int = 1000,
                         random_state: int = 42) -> Tuple[np.ndarray, ...]:
    """
    Convenience function to create a sample spiral dataset with splits.

    Args:
        n_samples: Number of samples to generate
        random_state: Random seed for reproducibility

    Returns:
        Tuple of (X_train, X_val, X_test, y_train, y_val, y_test)
    """
    generator = SpiralDataGenerator(random_state=random_state)
    X, y = generator.generate_spiral(n_samples=n_samples)
    return generator.train_test_split(X, y)


if __name__ == "__main__":
    # Demo usage
    print("Generating spiral dataset...")

    # Create generator
    generator = SpiralDataGenerator(random_state=42)

    # Generate data
    X, y = generator.generate_spiral(n_samples=1000, n_turns=2.5, noise_level=0.1)

    # Split data
    X_train, X_val, X_test, y_train, y_val, y_test = generator.train_test_split(X, y)

    # Create visualizer
    visualizer = DataVisualizer()

    # Print dataset information
    print(f"Dataset shapes:")
    print(f"  Training: X={X_train.shape}, y={y_train.shape}")
    print(f"  Validation: X={X_val.shape}, y={y_val.shape}")
    print(f"  Test: X={X_test.shape}, y={y_test.shape}")

    print(f"\nTarget statistics:")
    print(f"  Min: {y.min():.3f}")
    print(f"  Max: {y.max():.3f}")
    print(f"  Mean: {y.mean():.3f}")
    print(f"  Std: {y.std():.3f}")

    # Visualize data
    visualizer.plot_spiral_data(X, y, "Complete Spiral Dataset")
    visualizer.plot_data_splits(X_train, X_val, X_test, y_train, y_val, y_test)
    visualizer.plot_target_distribution(y, "Target Value Distribution")