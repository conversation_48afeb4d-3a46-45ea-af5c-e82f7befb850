"""
Custom Neural Network Implementation for Regression

This module provides a from-scratch implementation of a multi-layer perceptron
for regression tasks, with full forward and backward propagation.
"""

import numpy as np
import matplotlib.pyplot as plt
from typing import List, Tuple, Optional, Callable, Dict
import time


class ActivationFunction:
    """
    Container for activation functions and their derivatives.
    """

    @staticmethod
    def relu(x: np.ndarray) -> np.ndarray:
        """ReLU activation function."""
        return np.maximum(0, x)

    @staticmethod
    def relu_derivative(x: np.ndarray) -> np.ndarray:
        """Derivative of ReLU activation function."""
        return (x > 0).astype(float)

    @staticmethod
    def sigmoid(x: np.ndarray) -> np.ndarray:
        """Sigmoid activation function."""
        # Clip x to prevent overflow
        x_clipped = np.clip(x, -500, 500)
        return 1 / (1 + np.exp(-x_clipped))

    @staticmethod
    def sigmoid_derivative(x: np.ndarray) -> np.ndarray:
        """Derivative of sigmoid activation function."""
        s = ActivationFunction.sigmoid(x)
        return s * (1 - s)

    @staticmethod
    def tanh(x: np.ndarray) -> np.ndarray:
        """Hyperbolic tangent activation function."""
        return np.tanh(x)

    @staticmethod
    def tanh_derivative(x: np.ndarray) -> np.ndarray:
        """Derivative of hyperbolic tangent activation function."""
        return 1 - np.tanh(x) ** 2

    @staticmethod
    def linear(x: np.ndarray) -> np.ndarray:
        """Linear activation function (identity)."""
        return x

    @staticmethod
    def linear_derivative(x: np.ndarray) -> np.ndarray:
        """Derivative of linear activation function."""
        return np.ones_like(x)


class LossFunction:
    """
    Container for loss functions and their derivatives.
    """

    @staticmethod
    def mse(y_true: np.ndarray, y_pred: np.ndarray) -> float:
        """Mean Squared Error loss."""
        return np.mean((y_true - y_pred) ** 2)

    @staticmethod
    def mse_derivative(y_true: np.ndarray, y_pred: np.ndarray) -> np.ndarray:
        """Derivative of MSE loss with respect to predictions."""
        return 2 * (y_pred - y_true) / len(y_true)

    @staticmethod
    def mae(y_true: np.ndarray, y_pred: np.ndarray) -> float:
        """Mean Absolute Error loss."""
        return np.mean(np.abs(y_true - y_pred))

    @staticmethod
    def mae_derivative(y_true: np.ndarray, y_pred: np.ndarray) -> np.ndarray:
        """Derivative of MAE loss with respect to predictions."""
        return np.sign(y_pred - y_true) / len(y_true)


class Layer:
    """
    Represents a single layer in the neural network.
    """

    def __init__(self, input_size: int, output_size: int,
                 activation: str = 'relu', random_state: Optional[int] = None):
        """
        Initialize a neural network layer.

        Args:
            input_size: Number of input features
            output_size: Number of output neurons
            activation: Activation function name ('relu', 'sigmoid', 'tanh', 'linear')
            random_state: Random seed for weight initialization
        """
        if random_state is not None:
            np.random.seed(random_state)

        # Initialize weights using Xavier/Glorot initialization
        self.weights = np.random.randn(input_size, output_size) * np.sqrt(2.0 / input_size)
        self.biases = np.zeros((1, output_size))

        # Set activation function
        self.activation_name = activation
        if activation == 'relu':
            self.activation = ActivationFunction.relu
            self.activation_derivative = ActivationFunction.relu_derivative
        elif activation == 'sigmoid':
            self.activation = ActivationFunction.sigmoid
            self.activation_derivative = ActivationFunction.sigmoid_derivative
        elif activation == 'tanh':
            self.activation = ActivationFunction.tanh
            self.activation_derivative = ActivationFunction.tanh_derivative
        elif activation == 'linear':
            self.activation = ActivationFunction.linear
            self.activation_derivative = ActivationFunction.linear_derivative
        else:
            raise ValueError(f"Unknown activation function: {activation}")

        # Store intermediate values for backpropagation
        self.last_input = None
        self.last_z = None  # Pre-activation values
        self.last_output = None  # Post-activation values

    def forward(self, x: np.ndarray) -> np.ndarray:
        """
        Forward pass through the layer.

        Args:
            x: Input data (batch_size, input_size)

        Returns:
            Output after applying weights, biases, and activation
        """
        self.last_input = x
        self.last_z = np.dot(x, self.weights) + self.biases
        self.last_output = self.activation(self.last_z)
        return self.last_output

    def backward(self, grad_output: np.ndarray) -> np.ndarray:
        """
        Backward pass through the layer.

        Args:
            grad_output: Gradient of loss with respect to layer output

        Returns:
            Gradient of loss with respect to layer input
        """
        # Gradient with respect to pre-activation values
        grad_z = grad_output * self.activation_derivative(self.last_z)

        # Gradients with respect to weights and biases
        self.grad_weights = np.dot(self.last_input.T, grad_z)
        self.grad_biases = np.sum(grad_z, axis=0, keepdims=True)

        # Gradient with respect to input
        grad_input = np.dot(grad_z, self.weights.T)

        return grad_input


class NeuralNetwork:
    """
    Multi-layer perceptron for regression tasks.
    """

    def __init__(self, layer_sizes: List[int], activations: List[str],
                 loss_function: str = 'mse', random_state: Optional[int] = None):
        """
        Initialize the neural network.

        Args:
            layer_sizes: List of layer sizes [input_size, hidden1, hidden2, ..., output_size]
            activations: List of activation functions for each layer (except input)
            loss_function: Loss function name ('mse' or 'mae')
            random_state: Random seed for reproducible results
        """
        self.layer_sizes = layer_sizes
        self.activations = activations
        self.random_state = random_state

        # Validate inputs
        if len(layer_sizes) < 2:
            raise ValueError("Need at least input and output layer")
        if len(activations) != len(layer_sizes) - 1:
            raise ValueError("Number of activations must equal number of layers - 1")

        # Initialize layers
        self.layers = []
        for i in range(len(layer_sizes) - 1):
            layer = Layer(
                input_size=layer_sizes[i],
                output_size=layer_sizes[i + 1],
                activation=activations[i],
                random_state=random_state + i if random_state is not None else None
            )
            self.layers.append(layer)

        # Set loss function
        if loss_function == 'mse':
            self.loss_function = LossFunction.mse
            self.loss_derivative = LossFunction.mse_derivative
        elif loss_function == 'mae':
            self.loss_function = LossFunction.mae
            self.loss_derivative = LossFunction.mae_derivative
        else:
            raise ValueError(f"Unknown loss function: {loss_function}")

        # Training history
        self.history = {
            'train_loss': [],
            'val_loss': [],
            'epochs': []
        }

    def forward(self, x: np.ndarray) -> np.ndarray:
        """
        Forward pass through the entire network.

        Args:
            x: Input data (batch_size, input_size)

        Returns:
            Network output (batch_size, output_size)
        """
        current_input = x
        for layer in self.layers:
            current_input = layer.forward(current_input)
        return current_input

    def backward(self, y_true: np.ndarray, y_pred: np.ndarray) -> None:
        """
        Backward pass through the entire network.

        Args:
            y_true: True target values
            y_pred: Predicted values from forward pass
        """
        # Start with gradient of loss with respect to output
        grad_output = self.loss_derivative(y_true, y_pred)

        # Backpropagate through layers in reverse order
        for layer in reversed(self.layers):
            grad_output = layer.backward(grad_output)

    def update_weights(self, learning_rate: float) -> None:
        """
        Update network weights using computed gradients.

        Args:
            learning_rate: Learning rate for gradient descent
        """
        for layer in self.layers:
            layer.weights -= learning_rate * layer.grad_weights
            layer.biases -= learning_rate * layer.grad_biases

    def predict(self, x: np.ndarray) -> np.ndarray:
        """
        Make predictions on input data.

        Args:
            x: Input data (batch_size, input_size)

        Returns:
            Predictions (batch_size, output_size)
        """
        return self.forward(x)

    def train(self, X_train: np.ndarray, y_train: np.ndarray,
              X_val: Optional[np.ndarray] = None, y_val: Optional[np.ndarray] = None,
              epochs: int = 100, learning_rate: float = 0.01,
              batch_size: Optional[int] = None, verbose: bool = True) -> Dict:
        """
        Train the neural network.

        Args:
            X_train: Training features (n_samples, n_features)
            y_train: Training targets (n_samples,) or (n_samples, 1)
            X_val: Validation features (optional)
            y_val: Validation targets (optional)
            epochs: Number of training epochs
            learning_rate: Learning rate for gradient descent
            batch_size: Batch size for mini-batch training (None for full batch)
            verbose: Whether to print training progress

        Returns:
            Training history dictionary
        """
        # Ensure targets are 2D
        if y_train.ndim == 1:
            y_train = y_train.reshape(-1, 1)
        if y_val is not None and y_val.ndim == 1:
            y_val = y_val.reshape(-1, 1)

        n_samples = X_train.shape[0]
        if batch_size is None:
            batch_size = n_samples

        start_time = time.time()

        for epoch in range(epochs):
            # Shuffle training data
            if self.random_state is not None:
                np.random.seed(self.random_state + epoch)
            indices = np.random.permutation(n_samples)
            X_shuffled = X_train[indices]
            y_shuffled = y_train[indices]

            # Mini-batch training
            epoch_loss = 0.0
            n_batches = 0

            for i in range(0, n_samples, batch_size):
                batch_end = min(i + batch_size, n_samples)
                X_batch = X_shuffled[i:batch_end]
                y_batch = y_shuffled[i:batch_end]

                # Forward pass
                y_pred = self.forward(X_batch)

                # Compute loss
                batch_loss = self.loss_function(y_batch, y_pred)
                epoch_loss += batch_loss
                n_batches += 1

                # Backward pass
                self.backward(y_batch, y_pred)

                # Update weights
                self.update_weights(learning_rate)

            # Average loss for the epoch
            avg_train_loss = epoch_loss / n_batches
            self.history['train_loss'].append(avg_train_loss)

            # Validation loss
            if X_val is not None and y_val is not None:
                val_pred = self.predict(X_val)
                val_loss = self.loss_function(y_val, val_pred)
                self.history['val_loss'].append(val_loss)

            self.history['epochs'].append(epoch + 1)

            # Print progress
            if verbose and (epoch + 1) % max(1, epochs // 10) == 0:
                elapsed = time.time() - start_time
                if X_val is not None:
                    print(f"Epoch {epoch + 1}/{epochs} - "
                          f"Train Loss: {avg_train_loss:.6f} - "
                          f"Val Loss: {val_loss:.6f} - "
                          f"Time: {elapsed:.2f}s")
                else:
                    print(f"Epoch {epoch + 1}/{epochs} - "
                          f"Train Loss: {avg_train_loss:.6f} - "
                          f"Time: {elapsed:.2f}s")

        return self.history

    def evaluate(self, X: np.ndarray, y: np.ndarray) -> Dict[str, float]:
        """
        Evaluate the model on given data.

        Args:
            X: Input features
            y: True targets

        Returns:
            Dictionary of evaluation metrics
        """
        if y.ndim == 1:
            y = y.reshape(-1, 1)

        y_pred = self.predict(X)

        mse = LossFunction.mse(y, y_pred)
        mae = LossFunction.mae(y, y_pred)

        # R-squared
        ss_res = np.sum((y - y_pred) ** 2)
        ss_tot = np.sum((y - np.mean(y)) ** 2)
        r2 = 1 - (ss_res / ss_tot) if ss_tot != 0 else 0

        return {
            'mse': mse,
            'mae': mae,
            'rmse': np.sqrt(mse),
            'r2': r2
        }

    def plot_training_history(self, save_path: Optional[str] = None) -> None:
        """
        Plot training and validation loss curves.

        Args:
            save_path: Optional path to save the plot
        """
        plt.figure(figsize=(10, 6))

        plt.plot(self.history['epochs'], self.history['train_loss'],
                label='Training Loss', linewidth=2)

        if self.history['val_loss']:
            plt.plot(self.history['epochs'], self.history['val_loss'],
                    label='Validation Loss', linewidth=2)

        plt.xlabel('Epoch')
        plt.ylabel('Loss')
        plt.title('Training History')
        plt.legend()
        plt.grid(True, alpha=0.3)

        if save_path:
            plt.savefig(save_path, dpi=300, bbox_inches='tight')

        plt.show()

    def get_network_info(self) -> str:
        """
        Get a string representation of the network architecture.

        Returns:
            String describing the network
        """
        info = "Neural Network Architecture:\n"
        info += "=" * 30 + "\n"

        total_params = 0
        for i, layer in enumerate(self.layers):
            layer_params = layer.weights.size + layer.biases.size
            total_params += layer_params

            info += f"Layer {i + 1}: {layer.weights.shape[0]} -> {layer.weights.shape[1]} "
            info += f"({layer.activation_name}) - {layer_params:,} parameters\n"

        info += "=" * 30 + "\n"
        info += f"Total parameters: {total_params:,}\n"

        return info


def create_sample_network(input_size: int = 2, random_state: int = 42) -> NeuralNetwork:
    """
    Create a sample neural network for spiral regression.

    Args:
        input_size: Number of input features
        random_state: Random seed for reproducibility

    Returns:
        Configured neural network
    """
    # Architecture: input -> 64 -> 32 -> 16 -> 1
    layer_sizes = [input_size, 64, 32, 16, 1]
    activations = ['relu', 'relu', 'relu', 'linear']

    return NeuralNetwork(
        layer_sizes=layer_sizes,
        activations=activations,
        loss_function='mse',
        random_state=random_state
    )


if __name__ == "__main__":
    # Demo usage
    print("Testing Neural Network Implementation...")

    # Create sample data
    np.random.seed(42)
    X = np.random.randn(100, 2)
    y = X[:, 0] ** 2 + X[:, 1] ** 2 + 0.1 * np.random.randn(100)  # Simple quadratic function

    # Split data
    split_idx = 80
    X_train, X_test = X[:split_idx], X[split_idx:]
    y_train, y_test = y[:split_idx], y[split_idx:]

    # Create and train network
    network = create_sample_network(input_size=2, random_state=42)
    print(network.get_network_info())

    # Train the network
    print("Training network...")
    history = network.train(
        X_train, y_train, X_test, y_test,
        epochs=100, learning_rate=0.01, verbose=True
    )

    # Evaluate
    train_metrics = network.evaluate(X_train, y_train)
    test_metrics = network.evaluate(X_test, y_test)

    print(f"\nTraining Metrics:")
    for metric, value in train_metrics.items():
        print(f"  {metric.upper()}: {value:.6f}")

    print(f"\nTest Metrics:")
    for metric, value in test_metrics.items():
        print(f"  {metric.upper()}: {value:.6f}")

    # Plot training history
    network.plot_training_history()