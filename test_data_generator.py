"""
Test script for the spiral data generator.
This script tests the data generation and saves sample plots.
"""

import sys
import os
sys.path.append('src')

from data_generator import SpiralDataGenerator, DataVisualizer, create_sample_dataset
import matplotlib.pyplot as plt
import numpy as np

def test_data_generation():
    """Test basic data generation functionality."""
    print("Testing data generation...")

    # Create generator
    generator = SpiralDataGenerator(random_state=42)

    # Test different configurations
    configs = [
        {"n_samples": 500, "n_turns": 1.5, "noise_level": 0.05, "radius_scale": 0.8},
        {"n_samples": 800, "n_turns": 3.0, "noise_level": 0.15, "radius_scale": 1.2},
        {"n_samples": 1000, "n_turns": 2.5, "noise_level": 0.1, "radius_scale": 1.0}
    ]

    for i, config in enumerate(configs):
        print(f"\nConfiguration {i+1}: {config}")
        X, y = generator.generate_spiral(**config)

        print(f"  Generated X shape: {X.shape}")
        print(f"  Generated y shape: {y.shape}")
        print(f"  X range: [{X.min():.3f}, {X.max():.3f}]")
        print(f"  y range: [{y.min():.3f}, {y.max():.3f}]")

        # Test train/test split
        splits = generator.train_test_split(X, y)
        X_train, X_val, X_test, y_train, y_val, y_test = splits

        total_samples = len(X_train) + len(X_val) + len(X_test)
        print(f"  Split verification: {total_samples} == {len(X)} ? {total_samples == len(X)}")

def test_visualization():
    """Test visualization functionality."""
    print("\nTesting visualization...")

    # Create sample data
    X_train, X_val, X_test, y_train, y_val, y_test = create_sample_dataset(
        n_samples=800, random_state=42
    )

    # Create visualizer
    visualizer = DataVisualizer(figsize=(10, 6))

    # Create output directory
    os.makedirs('outputs', exist_ok=True)

    # Test individual plots (save instead of show to avoid blocking)
    plt.ioff()  # Turn off interactive mode

    # Plot complete dataset
    X_complete = np.vstack([X_train, X_val, X_test])
    y_complete = np.hstack([y_train, y_val, y_test])

    visualizer.plot_spiral_data(X_complete, y_complete,
                               "Test Spiral Dataset",
                               save_path="outputs/spiral_dataset.png")
    plt.close()

    # Plot data splits
    visualizer.plot_data_splits(X_train, X_val, X_test, y_train, y_val, y_test,
                               save_path="outputs/data_splits.png")
    plt.close()

    # Plot target distribution
    visualizer.plot_target_distribution(y_complete, "Test Target Distribution",
                                       save_path="outputs/target_distribution.png")
    plt.close()

    plt.ion()  # Turn interactive mode back on

    print("  Plots saved to outputs/ directory")

def test_edge_cases():
    """Test edge cases and error handling."""
    print("\nTesting edge cases...")

    generator = SpiralDataGenerator(random_state=42)

    # Test with minimal samples
    X, y = generator.generate_spiral(n_samples=10)
    assert X.shape == (10, 2), f"Expected (10, 2), got {X.shape}"
    assert y.shape == (10,), f"Expected (10,), got {y.shape}"
    print("  ✓ Minimal samples test passed")

    # Test with no noise
    X, y = generator.generate_spiral(n_samples=100, noise_level=0.0)
    print("  ✓ No noise test passed")

    # Test with high noise
    X, y = generator.generate_spiral(n_samples=100, noise_level=0.5)
    print("  ✓ High noise test passed")

    # Test reproducibility
    gen1 = SpiralDataGenerator(random_state=123)
    gen2 = SpiralDataGenerator(random_state=123)

    X1, y1 = gen1.generate_spiral(n_samples=100)
    X2, y2 = gen2.generate_spiral(n_samples=100)

    assert np.allclose(X1, X2), "Random state not working for X"
    assert np.allclose(y1, y2), "Random state not working for y"
    print("  ✓ Reproducibility test passed")

if __name__ == "__main__":
    print("=" * 50)
    print("SPIRAL DATA GENERATOR TEST SUITE")
    print("=" * 50)

    try:
        test_data_generation()
        test_visualization()
        test_edge_cases()

        print("\n" + "=" * 50)
        print("✅ ALL TESTS PASSED!")
        print("✅ Phase 1: Data Generation & Visualization COMPLETE")
        print("=" * 50)

    except Exception as e:
        print(f"\n❌ TEST FAILED: {e}")
        import traceback
        traceback.print_exc()